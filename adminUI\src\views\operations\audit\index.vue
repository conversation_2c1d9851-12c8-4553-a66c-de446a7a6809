<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <el-tabs
        v-model="searchForm.status"
        @tab-click="onChangeType"
        class="mb20"
      >
        <el-tab-pane :label="$t('common.pendingReview')" name="0"></el-tab-pane>
        <el-tab-pane
          :label="$t('common.reviewedPassed')"
          name="2"
        ></el-tab-pane>
        <el-tab-pane
          :label="$t('common.reviewedRejected')"
          name="1"
        ></el-tab-pane>
      </el-tabs>
      <div class="container mt-1">
        <el-radio-group
          v-model="searchForm.extractType"
          @change="getList(1)"
          size="small"
          style="margin-bottom: 18px;"
        >
          <el-radio-button label="wallet" value="wallet">{{
            $t("operations.withdrawal.walletWithdrawal")
          }}</el-radio-button>
          <el-radio-button label="bank" value="bank">{{
            $t("operations.withdrawal.bankWithdrawal")
          }}</el-radio-button>
        </el-radio-group>
        <el-form v-model="searchForm" inline size="small">
          <el-form-item :label="$t('operations.withdrawal.applicant') + '：'">
            <el-input
              v-model="searchForm.keywords"
              size="small"
              :placeholder="$t('common.enter')"
            ></el-input>
          </el-form-item>
          <el-form-item
            :label="$t('operations.withdrawal.applicationTime') + '：'"
          >
            <el-date-picker
              v-model="timeList"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              size="small"
              type="daterange"
              placement="bottom-end"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
              style="width: 250px;"
            />
          </el-form-item>
          <el-form-item
            :label="$t('operations.withdrawal.electronicWallet') + '：'"
            v-if="searchForm.extractType == 'wallet'"
          >
            <el-select
              v-model="searchForm.walletCode"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                v-for="item in walletList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            :label="$t('operations.withdrawal.bankName') + '：'"
            v-if="searchForm.extractType == 'bank'"
          >
            <el-select
              v-model="searchForm.bankName"
              :placeholder="$t('common.all')"
            >
              <el-option
                v-for="(item, index) in bankList"
                :key="index"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <el-button size="small" type="primary" class="mr10" @click="getList(1)">{{
        $t("common.query")
      }}</el-button>
      <el-button size="small" type="" class="mr10" @click="resetForm">{{
        $t("common.reset")
      }}</el-button>
    </el-card>
    <el-card class="box-card" style="margin-top: 12px;">
      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column type="index" :label="$t('common.serialNumber')"  width="110">
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.applicationId')"
          min-width="80"
          prop="uid"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.applicantName')"
          min-width="80"
          prop="realName"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.withdrawalAmount')"
          min-width="80"
          prop="extractPrice"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.serviceFee')"
          min-width="80"
          prop="serviceFee"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.actualAmount')"
          min-width="100"
          prop="actualAmount"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.applicationTime')"
          min-width="80"
          prop="createTime"
        >
        </el-table-column>
        <el-table-column
          v-if="searchForm.extractType === 'wallet'"
          :label="$t('operations.withdrawal.walletCode')"
          min-width="80"
          prop="walletCode"
        >
        </el-table-column>
        <el-table-column
          v-if="searchForm.extractType === 'wallet'"
          :label="$t('operations.withdrawal.walletAccount')"
          min-width="80"
          prop="walletAccount"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.walletAccount | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="searchForm.extractType === 'bank'"
          :label="$t('operations.withdrawal.bankName')"
          min-width="80"
          prop="bankName"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.bankName | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="searchForm.extractType === 'bank'"
          :label="$t('operations.withdrawal.bankCardNumber')"
          min-width="80"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.name')"
          min-width="80"
          prop="nickName"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.phoneNumber')"
          min-width="80"
          prop="phone"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.withdrawalCount')"
          min-width="80"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.auditResult')"
          min-width="80"
          v-if="searchForm.status !== '0'"
        >
        </el-table-column>
        <el-table-column
          :label="$t('operations.withdrawal.rejectReason')"
          min-width="80"
          v-if="searchForm.status === '2'"
        >
        </el-table-column>
        <el-table-column
          :label="$t('product.action')"
          min-width="80"
          v-if="searchForm.status === '0'"
        >
          <template slot-scope="scope">
            <el-button
              size="small"
              type="text"
              @click="handleApi(scope.row, 1)"
              >{{ $t("operations.withdrawal.approve") }}</el-button
            >
            <el-button
              size="small"
              type="text"
              @click="handleOpen(scope.row, -1)"
              >{{ $t("operations.withdrawal.reject") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="mt20"
        @size-change="sizeChange"
        @current-change="pageChange"
        :current-page="searchForm.page"
        :page-sizes="[20, 40, 60, 100]"
        :page-size="searchForm.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="searchForm.total"
      >
      </el-pagination>
      <el-dialog
        append-to-body
        :visible.sync="dialogFormVisible"
        :title="$t('operations.withdrawal.rejectReview')"
        width="680px"
        @close="handleCancle"
      >
        <el-form
          ref="elForm"
          inline
          :model="artFrom"
          :rules="rules"
          label-width="200px"
        >
          <el-form-item
            :label="$t('operations.withdrawal.rejectReason') + '：'"
            prop="backMessage"
          >
            <el-input
              v-model="artFrom.backMessage"
              size="small"
              :placeholder="$t('common.enterRejectReason')"
              class="w300"
            ></el-input>
          </el-form-item>
        </el-form>

        <div slot="footer">
          <el-button type="primary" @click="handelConfirm">
            {{ $t("common.confirm") }}
          </el-button>
          <el-button @click="handleCancle">
            {{ $t("common.cancel") }}
          </el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { applyListApi, extractBankApi, financeApplyApi } from "@/api/financial";
export default {
  name: "WithdrawalRequest",
  data() {
    return {
      loading: false,
      tableData: [],
      artFrom: { backMessage: "" },
      searchForm: {
        tableFromType: "",
        keywords: "",
        dateLimit: "",
        bankName: "",
        walletCode: "",
        extractType: "wallet",
        status: 0,
        page: 1,
        limit: 20,
        total: 0
      },
      timeList: [],
      dialogFormVisible: false,
      walletList: [
        { label: "ShopeePay", value: "ShopeePay" },
        { label: "DANA", value: "DANA" },
        { label: "OVO", value: "OVO" },
        { label: "Gopay", value: "Gopay" }
      ],
      bankList: [],

      rules: {
        backMessage: [
          {
            required: true,
            message: "请输入",
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {},
  mounted() {
    this.getList();
    this.getBankList();
  },
  methods: {
    // 获取银行列表
    getBankList() {
      extractBankApi()
        .then(res => {
          this.bankList = res;
        })
        .catch(() => {});
    },
    // 列表
    getList(num) {
      this.loading = true;
      this.searchForm.page = num ? num : this.searchForm.page;
      this.searchForm.dateLimit = this.timeList.length
        ? this.timeList.join(",")
        : "";
      applyListApi(this.searchForm)
        .then(res => {
          this.tableData = res.list;
          this.searchForm.total = res.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    resetForm() {
      this.searchForm = {
        keywords: "",
        dateLimit: "",
        bankName: "",
        walletCode: "",
        extractType: this.searchForm.extractType,
        page: 1,
        limit: 20,
        total: 0
      };
      this.timeList = [];
      this.getList();
    },
    //切换页数
    pageChange(index) {
      this.searchForm.page = index;
      this.getList();
    },
    //切换显示条数
    sizeChange(index) {
      this.searchForm.limit = index;
      this.getList();
    },
    handleUpload() {},
    onChangeType(tab) {
      this.getList();
    },
    handleOpen(row, status) {
      this.artFrom.id = row.id;
      this.artFrom.status = status;
      this.dialogFormVisible = true;
    },
    handleCancle() {
      this.dialogFormVisible = false;
      this.artFrom = {
        backMessage: ""
      };
    },
    handleApi(row, status) {
      if (row.id) {
        this.artFrom.id = row.id;
        this.artFrom.status = status;
      }

      financeApplyApi(this.artFrom)
        .then(res => {
          if (res.code == 200) {
            this.$message.success(this.$t("common.operationSuccess"));
          } else {
            this.$message.error(this.$t("common.operationFailed"));
          }
        })
        .finally(() => {
          this.resetForm();
        });
    },
    handelConfirm() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return;
        this.handleApi({}, -1);
      });
    }
  }
};
</script>
<style scoped lang="scss">
/**/
</style>
