package com.genco.service.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.system.SystemUserLevel;
import com.genco.common.model.user.User;
import com.genco.common.model.user.UserLevel;
import com.genco.common.model.user.UserLevelUpgradeLog;
import com.genco.common.model.user.UserLevelUpgradeOrder;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.UserLevelUpgradeOrderSearchRequest;
import com.genco.common.request.UserLevelUpgradeRequest;
import com.genco.common.response.UserLevelUpgradeResponse;
import com.genco.common.utils.CrmebUtil;
import com.genco.service.dao.UserLevelUpgradeLogDao;
import com.genco.service.dao.UserLevelUpgradeOrderDao;
import com.genco.service.service.*;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户等级升级服务实现类
 */
@Slf4j
@Service
public class UserLevelUpgradeServiceImpl extends ServiceImpl<UserLevelUpgradeOrderDao, UserLevelUpgradeOrder> implements UserLevelUpgradeService {

    @Autowired
    private UserService userService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Autowired
    private UserLevelService userLevelService;

    @Autowired
    private UserLevelUpgradeLogDao userLevelUpgradeLogDao;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    public List<UserLevelUpgradeOrder> getList(PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<UserLevelUpgradeOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(UserLevelUpgradeOrder::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<UserLevelUpgradeOrder> getList(UserLevelUpgradeOrderSearchRequest searchRequest, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<UserLevelUpgradeOrder> wrapper = new LambdaQueryWrapper<>();

        // 添加搜索条件
        if (StrUtil.isNotBlank(searchRequest.getOrderNo())) {
            wrapper.like(UserLevelUpgradeOrder::getOrderNo, searchRequest.getOrderNo());
        }

        if (ObjectUtil.isNotNull(searchRequest.getOrderStatus())) {
            wrapper.eq(UserLevelUpgradeOrder::getOrderStatus, searchRequest.getOrderStatus());
        }

        if (ObjectUtil.isNotNull(searchRequest.getUid())) {
            wrapper.eq(UserLevelUpgradeOrder::getUid, searchRequest.getUid());
        }

        if (StrUtil.isNotBlank(searchRequest.getPaymentMethod())) {
            wrapper.eq(UserLevelUpgradeOrder::getPaymentMethod, searchRequest.getPaymentMethod());
        }

        // 时间范围查询
        if (StrUtil.isNotBlank(searchRequest.getStartTime())) {
            wrapper.ge(UserLevelUpgradeOrder::getCreateTime, searchRequest.getStartTime());
        }

        if (StrUtil.isNotBlank(searchRequest.getEndTime())) {
            wrapper.le(UserLevelUpgradeOrder::getCreateTime, searchRequest.getEndTime());
        }

        wrapper.orderByDesc(UserLevelUpgradeOrder::getCreateTime);
        return list(wrapper);
    }

    @Override
    public UserLevelUpgradeResponse purchaseUpgrade(UserLevelUpgradeRequest request) {
        // 1. 获取当前用户
        User currentUser = userService.getInfo();
        if (ObjectUtil.isNull(currentUser)) {
            throw new CrmebException("用户未登录");
        }

        // 2. 检查是否可以升级
        if (!canUpgrade(currentUser.getUid(), request.getToLevelId())) {
            throw new CrmebException("不满足升级条件");
        }

        // 3. 获取等级信息
        SystemUserLevel fromLevel = systemUserLevelService.getByLevelId(currentUser.getLevel());
        SystemUserLevel toLevel = systemUserLevelService.getByLevelId(request.getToLevelId());
        
        if (ObjectUtil.isNull(fromLevel) || ObjectUtil.isNull(toLevel)) {
            throw new CrmebException("等级信息不存在");
        }

        // 4. 计算升级费用
        BigDecimal upgradePrice = getUpgradePrice(currentUser.getLevel(), request.getToLevelId());

        // 5. 检查用户余额
        if (currentUser.getNowMoney().compareTo(upgradePrice) < 0) {
            throw new CrmebException("余额不足，无法完成升级");
        }

        // 6. 创建升级订单
        UserLevelUpgradeOrder order = new UserLevelUpgradeOrder();
        order.setOrderNo(generateOrderNo());
        order.setUid(currentUser.getUid());
        order.setFromLevelId(currentUser.getLevel());
        order.setToLevelId(request.getToLevelId());
        order.setUpgradePrice(upgradePrice);
        order.setPaymentMethod(request.getPaymentMethod());
        order.setOrderStatus(UserLevelUpgradeOrder.STATUS_PENDING);
        order.setRemark(request.getRemark());

        // 7. 执行升级事务
        Boolean success = transactionTemplate.execute(status -> {
            try {
                // 保存订单
                save(order);
                
                // 立即处理支付（余额支付）
                return handlePaymentSuccess(order.getOrderNo());
            } catch (Exception e) {
                log.error("等级升级失败", e);
                status.setRollbackOnly();
                return false;
            }
        });

        // 8. 构建响应
        UserLevelUpgradeResponse response = new UserLevelUpgradeResponse();
        response.setOrderNo(order.getOrderNo());
        response.setFromLevelName(fromLevel.getName());
        response.setToLevelName(toLevel.getName());
        response.setUpgradePrice(upgradePrice);
        response.setPaymentMethod(request.getPaymentMethod());
        response.setOrderStatus(success ? UserLevelUpgradeOrder.STATUS_PAID : UserLevelUpgradeOrder.STATUS_CANCELLED);
        response.setSuccess(success);
        response.setMessage(success ? "升级成功" : "升级失败");

        return response;
    }

    @Override
    public Boolean canUpgrade(Integer uid, Integer toLevelId) {
        // 1. 获取用户信息
        User user = userService.getInfoByUid(uid);
        if (ObjectUtil.isNull(user)) {
            return false;
        }

        // 2. 获取目标等级信息
        SystemUserLevel toLevel = systemUserLevelService.getByLevelId(toLevelId);
        if (ObjectUtil.isNull(toLevel) || !toLevel.getIsAvailable()) {
            return false;
        }

        // 3. 检查升级条件
        // 只能从低等级升级到高等级
        if (user.getLevel() >= toLevelId) {
            return false;
        }

        // 4. 检查升级方式
        // 目前只支持付费购买银牌等级（从普通用户升级到银牌用户）
        if (user.getLevel() == 1 && toLevelId == 2 && toLevel.getUpgradeType() == 1) {
            return true;
        }

        // 其他升级方式暂未开放
        return false;
    }

    @Override
    public BigDecimal getUpgradePrice(Integer fromLevelId, Integer toLevelId) {
        SystemUserLevel toLevel = systemUserLevelService.getByLevelId(toLevelId);
        if (ObjectUtil.isNull(toLevel)) {
            return BigDecimal.ZERO;
        }
        return toLevel.getUpgradePrice();
    }

    @Override
    public Boolean handlePaymentSuccess(String orderNo) {
        // 1. 获取订单信息
        LambdaQueryWrapper<UserLevelUpgradeOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserLevelUpgradeOrder::getOrderNo, orderNo);
        UserLevelUpgradeOrder order = getOne(wrapper);
        
        if (ObjectUtil.isNull(order)) {
            throw new CrmebException("订单不存在");
        }

        if (order.getOrderStatus() != UserLevelUpgradeOrder.STATUS_PENDING) {
            throw new CrmebException("订单状态异常");
        }

        // 2. 获取用户信息
        User user = userService.getInfoByUid(order.getUid());
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }

        // 3. 扣除用户余额
        BigDecimal newBalance = user.getNowMoney().subtract(order.getUpgradePrice());
        if (newBalance.compareTo(BigDecimal.ZERO) < 0) {
            throw new CrmebException("余额不足");
        }

        // 4. 更新用户等级和余额
        user.setLevel(order.getToLevelId());
        user.setNowMoney(newBalance);
        userService.updateById(user);

        // 5. 创建用户等级记录
        SystemUserLevel toLevel = systemUserLevelService.getByLevelId(order.getToLevelId());
        UserLevel userLevel = new UserLevel();
        userLevel.setUid(user.getUid());
        userLevel.setLevelId(order.getToLevelId());
        userLevel.setGrade(toLevel.getGrade());
        userLevel.setStatus(true);
        userLevel.setMark("付费升级到" + toLevel.getName());
        userLevel.setRemind(false);
        userLevel.setIsDel(false);
        userLevel.setDiscount(toLevel.getDiscount());
        userLevel.setApplyStatus(1);
        userLevel.setApplyTime(new Date());
        userLevel.setApproveTime(new Date());
        userLevel.setUpgradeOrderId(orderNo);
        userLevelService.save(userLevel);

        // 6. 更新订单状态
        order.setOrderStatus(UserLevelUpgradeOrder.STATUS_PAID);
        order.setPayTime(new Date());
        updateById(order);

        // 7. 记录升级日志
        UserLevelUpgradeLog log = new UserLevelUpgradeLog();
        log.setUid(user.getUid());
        log.setFromLevelId(order.getFromLevelId());
        log.setToLevelId(order.getToLevelId());
        log.setUpgradeType(UserLevelUpgradeLog.TYPE_PURCHASE);
        log.setOrderNo(orderNo);
        log.setRemark("用户付费升级：" + order.getUpgradePrice() + "印尼盾");
        userLevelUpgradeLogDao.insert(log);

        return true;
    }

    @Override
    public Boolean cancelOrder(String orderNo) {
        LambdaQueryWrapper<UserLevelUpgradeOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserLevelUpgradeOrder::getOrderNo, orderNo);
        UserLevelUpgradeOrder order = getOne(wrapper);
        
        if (ObjectUtil.isNull(order)) {
            return false;
        }

        if (order.getOrderStatus() != UserLevelUpgradeOrder.STATUS_PENDING) {
            return false;
        }

        order.setOrderStatus(UserLevelUpgradeOrder.STATUS_CANCELLED);
        order.setCancelTime(new Date());
        return updateById(order);
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "ULU" + System.currentTimeMillis() + CrmebUtil.randomCount(1000, 9999);
    }
}
