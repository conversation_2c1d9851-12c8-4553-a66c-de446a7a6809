{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\brand\\manage.vue?vue&type=template&id=5700eb87&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\brand\\manage.vue", "mtime": 1754443174079}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox relative\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\n                \"div\",\n                { staticClass: \"container mt-1\" },\n                [\n                  _c(\n                    \"el-form\",\n                    { attrs: { inline: \"\", size: \"small\" } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: _vm.$t(\"brand.search\") } },\n                        [\n                          _c(\"el-input\", {\n                            staticClass: \"selWidth\",\n                            attrs: {\n                              placeholder: _vm.$t(\"brand.brandNameInput\"),\n                              size: \"small\",\n                              clearable: \"\",\n                            },\n                            model: {\n                              value: _vm.form.name,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.form, \"name\", $$v)\n                              },\n                              expression: \"form.name\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: _vm.$t(\"brand.status\") } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              attrs: {\n                                placeholder: _vm.$t(\"brand.pleaseSelect\"),\n                              },\n                              model: {\n                                value: _vm.form.type,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.form, \"type\", $$v)\n                                },\n                                expression: \"form.type\",\n                              },\n                            },\n                            _vm._l(_vm.statusOptions, function (item) {\n                              return _c(\"el-option\", {\n                                key: item.value,\n                                attrs: {\n                                  label: _vm.$t(item.label),\n                                  value: item.value,\n                                },\n                              })\n                            }),\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  staticClass: \"mr10\",\n                  attrs: { size: \"small\", type: \"primary\" },\n                  on: { click: _vm.onSearch },\n                },\n                [_vm._v(_vm._s(_vm.$t(\"brand.query\")))]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  staticClass: \"mr10\",\n                  attrs: { size: \"small\", type: \"\" },\n                  on: { click: _vm.onReset },\n                },\n                [_vm._v(_vm._s(_vm.$t(\"brand.reset\")))]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"div\",\n                { staticClass: \"acea-row padtop-10\" },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { size: \"small\", type: \"success\" },\n                      on: { click: _vm.onAdd },\n                    },\n                    [_vm._v(_vm._s(_vm.$t(\"brand.addBrand\")))]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: {\n                        size: \"small\",\n                        disabled: _vm.multipleSelection.length === 0,\n                      },\n                      on: {\n                        click: function ($event) {\n                          return _vm.batchHandle(\"online\")\n                        },\n                      },\n                    },\n                    [_vm._v(_vm._s(_vm.$t(\"brand.batchOnline\")))]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: {\n                        size: \"small\",\n                        disabled: _vm.multipleSelection.length === 0,\n                      },\n                      on: {\n                        click: function ($event) {\n                          return _vm.batchHandle(\"outline\")\n                        },\n                      },\n                    },\n                    [_vm._v(_vm._s(_vm.$t(\"brand.batchOffline\")))]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: {\n                        size: \"small\",\n                        disabled: _vm.multipleSelection.length === 0,\n                      },\n                      on: {\n                        click: function ($event) {\n                          return _vm.batchHandle(\"delete\")\n                        },\n                      },\n                    },\n                    [_vm._v(_vm._s(_vm.$t(\"brand.batchDelete\")))]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.listLoading,\n                  expression: \"listLoading\",\n                },\n              ],\n              staticStyle: { width: \"100%\" },\n              attrs: {\n                data: _vm.tableData.data,\n                size: \"mini\",\n                \"highlight-current-row\": true,\n                \"header-cell-style\": { fontWeight: \"bold\" },\n              },\n              on: { \"selection-change\": _vm.handleSelectionChange },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { type: \"selection\", width: \"55\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: _vm.$t(\"brand.brandLogo\"), \"min-width\": \"80\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"demo-image__preview\" },\n                          [\n                            _c(\"el-image\", {\n                              staticStyle: { width: \"36px\", height: \"36px\" },\n                              attrs: {\n                                src: scope.row.logoUrl,\n                                \"preview-src-list\": [scope.row.logoUrl],\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"brand.brandName\"),\n                  \"min-width\": \"80\",\n                  prop: \"name\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"brand.industry\"),\n                  \"min-width\": \"160\",\n                  \"show-overflow-tooltip\": true,\n                  prop: \"industry\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"brand.platform\"),\n                  \"min-width\": \"90\",\n                  align: \"center\",\n                  prop: \"platform\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"brand.productCount\"),\n                  \"min-width\": \"100\",\n                  align: \"center\",\n                  prop: \"productCount\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"brand.createTime\"),\n                  \"min-width\": \"120\",\n                  align: \"center\",\n                  prop: \"gmtCreate\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"brand.creator\"),\n                  \"min-width\": \"120\",\n                  align: \"center\",\n                  prop: \"creator\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"brand.isHot\"),\n                  \"min-width\": \"120\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"el-switch\", {\n                          attrs: {\n                            \"active-value\": true,\n                            \"inactive-value\": false,\n                          },\n                          on: {\n                            change: function ($event) {\n                              return _vm.isHotChange(\n                                scope.row.id,\n                                scope.row.isHot\n                              )\n                            },\n                          },\n                          model: {\n                            value: scope.row.isHot,\n                            callback: function ($$v) {\n                              _vm.$set(scope.row, \"isHot\", $$v)\n                            },\n                            expression: \"scope.row.isHot\",\n                          },\n                        }),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"brand.isHighCashback\"),\n                  \"min-width\": \"120\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"el-switch\", {\n                          attrs: {\n                            \"active-value\": true,\n                            \"inactive-value\": false,\n                          },\n                          on: {\n                            change: function ($event) {\n                              return _vm.isHighCashbackChange(\n                                scope.row.id,\n                                scope.row.isHighCashback\n                              )\n                            },\n                          },\n                          model: {\n                            value: scope.row.isHighCashback,\n                            callback: function ($$v) {\n                              _vm.$set(scope.row, \"isHighCashback\", $$v)\n                            },\n                            expression: \"scope.row.isHighCashback\",\n                          },\n                        }),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"brand.statusLabel\"),\n                  \"min-width\": \"120\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-tag\",\n                          {\n                            attrs: {\n                              type: _vm.getStatusTagType(scope.row.status),\n                              size: \"small\",\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n            \" +\n                                _vm._s(_vm.getStatusText(scope.row.status)) +\n                                \"\\n          \"\n                            ),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"product.action\"),\n                  \"min-width\": \"150\",\n                  fixed: \"right\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"router-link\",\n                          {\n                            attrs: {\n                              to: {\n                                path: \"/brand/product/list\",\n                                query: { brand: scope.row.code },\n                              },\n                            },\n                          },\n                          [\n                            _c(\n                              \"el-button\",\n                              {\n                                staticClass: \"mr10\",\n                                attrs: { size: \"small\", type: \"text\" },\n                              },\n                              [_vm._v(_vm._s(_vm.$t(\"brand.productList\")))]\n                            ),\n                          ],\n                          1\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-button\",\n                          {\n                            staticClass: \"mr10\",\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.editBrand(scope.row, scope.$index)\n                              },\n                            },\n                          },\n                          [_vm._v(_vm._s(_vm.$t(\"brand.edit\")))]\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleDelete(\n                                  scope.row.id,\n                                  scope.$index\n                                )\n                              },\n                            },\n                          },\n                          [_vm._v(_vm._s(_vm.$t(\"brand.delete\")))]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [20, 40, 60, 80],\n                  \"page-size\": _vm.form.limit,\n                  \"current-page\": _vm.form.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.tableData.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChange,\n                  \"current-change\": _vm.pageChange,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: _vm.isEditMode\n              ? _vm.$t(\"brand.editDialogTitle\")\n              : _vm.$t(\"brand.addDialogTitle\"),\n            visible: _vm.brandDialogVisible,\n            width: \"500px\",\n            \"before-close\": _vm.handleCloseBrandDialog,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.brandDialogVisible = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-form\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.loading,\n                  expression: \"loading\",\n                },\n              ],\n              ref: \"dform\",\n              staticClass: \"mt20\",\n              attrs: { model: _vm.dform, \"label-width\": \"120px\" },\n              nativeOn: {\n                submit: function ($event) {\n                  $event.preventDefault()\n                },\n              },\n            },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"brand.brandName\") } },\n                [\n                  _c(\"el-input\", {\n                    staticClass: \"selWidth\",\n                    attrs: {\n                      placeholder: _vm.$t(\"brand.brandNameInput\"),\n                      size: \"small\",\n                      clearable: \"\",\n                    },\n                    model: {\n                      value: _vm.dform.name,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.dform, \"name\", $$v)\n                      },\n                      expression: \"dform.name\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"brand.brandLogo\") } },\n                [\n                  _c(\"el-input\", {\n                    staticClass: \"selWidth\",\n                    attrs: {\n                      placeholder: _vm.$t(\"brand.brandLogoInput\"),\n                      size: \"small\",\n                      clearable: \"\",\n                    },\n                    model: {\n                      value: _vm.dform.logoUrl,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.dform, \"logoUrl\", $$v)\n                      },\n                      expression: \"dform.logoUrl\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"brand.industry\") } },\n                [\n                  _c(\n                    \"el-select\",\n                    {\n                      attrs: { placeholder: _vm.$t(\"brand.pleaseSelect\") },\n                      model: {\n                        value: _vm.dform.industry,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.dform, \"industry\", $$v)\n                        },\n                        expression: \"dform.industry\",\n                      },\n                    },\n                    _vm._l(_vm.industryListOptions, function (item) {\n                      return _c(\"el-option\", {\n                        key: item.value,\n                        attrs: { label: item.label, value: item.value },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"brand.platform\") } },\n                [\n                  _c(\n                    \"el-select\",\n                    {\n                      attrs: { placeholder: _vm.$t(\"brand.pleaseSelect\") },\n                      model: {\n                        value: _vm.dform.platform,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.dform, \"platform\", $$v)\n                        },\n                        expression: \"dform.platform\",\n                      },\n                    },\n                    _vm._l(_vm.platformOptions, function (item) {\n                      return _c(\"el-option\", {\n                        key: item.value,\n                        attrs: { label: _vm.$t(item.label), value: item.value },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"brand.contactPerson\") } },\n                [\n                  _c(\"el-input\", {\n                    staticClass: \"selWidth\",\n                    attrs: {\n                      placeholder: _vm.$t(\"brand.contactPerson\"),\n                      size: \"small\",\n                      clearable: \"\",\n                    },\n                    model: {\n                      value: _vm.dform.contactPerson,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.dform, \"contactPerson\", $$v)\n                      },\n                      expression: \"dform.contactPerson\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"brand.contactPhone\") } },\n                [\n                  _c(\"el-input\", {\n                    staticClass: \"selWidth\",\n                    attrs: {\n                      placeholder: _vm.$t(\"brand.contactPhone\"),\n                      size: \"small\",\n                      clearable: \"\",\n                    },\n                    model: {\n                      value: _vm.dform.contactPhone,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.dform, \"contactPhone\", $$v)\n                      },\n                      expression: \"dform.contactPhone\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"brand.status\") } },\n                [\n                  _c(\n                    \"el-select\",\n                    {\n                      attrs: { placeholder: _vm.$t(\"brand.pleaseSelect\") },\n                      model: {\n                        value: _vm.dform.status,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.dform, \"status\", $$v)\n                        },\n                        expression: \"dform.status\",\n                      },\n                    },\n                    _vm._l(_vm.editStatusOptions, function (item) {\n                      return _c(\"el-option\", {\n                        key: item.value,\n                        attrs: { label: _vm.$t(item.label), value: item.value },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"span\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\n                \"el-button\",\n                { attrs: { type: \"primary\" }, on: { click: _vm.onSubBrand } },\n                [\n                  _vm._v(\n                    _vm._s(\n                      _vm.isEditMode\n                        ? _vm.$t(\"brand.update\")\n                        : _vm.$t(\"brand.confirm\")\n                    )\n                  ),\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\"el-button\", { on: { click: _vm.handleCloseBrandDialog } }, [\n                _vm._v(_vm._s(_vm.$t(\"brand.cancel\"))),\n              ]),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}