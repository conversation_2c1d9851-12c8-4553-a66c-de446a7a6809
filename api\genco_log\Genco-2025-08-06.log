{"@timestamp":"2025-08-06T08:55:28.474+08:00","@version":"1","message":"Application started with classpath: [file:/D:/Java/jre/lib/charsets.jar, file:/D:/Java/jre/lib/deploy.jar, file:/D:/Java/jre/lib/ext/access-bridge-64.jar, file:/D:/Java/jre/lib/ext/cldrdata.jar, file:/D:/Java/jre/lib/ext/dnsns.jar, file:/D:/Java/jre/lib/ext/jaccess.jar, file:/D:/Java/jre/lib/ext/localedata.jar, file:/D:/Java/jre/lib/ext/nashorn.jar, file:/D:/Java/jre/lib/ext/sunec.jar, file:/D:/Java/jre/lib/ext/sunjce_provider.jar, file:/D:/Java/jre/lib/ext/sunmscapi.jar, file:/D:/Java/jre/lib/ext/sunpkcs11.jar, file:/D:/Java/jre/lib/ext/zipfs.jar, file:/D:/Java/jre/lib/javaws.jar, file:/D:/Java/jre/lib/jce.jar, file:/D:/Java/jre/lib/jfr.jar, file:/D:/Java/jre/lib/jsse.jar, file:/D:/Java/jre/lib/management-agent.jar, file:/D:/Java/jre/lib/plugin.jar, file:/D:/Java/jre/lib/resources.jar, file:/D:/Java/jre/lib/rt.jar, file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-admin/target/classes/, file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-service/target/classes/, file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-common/target/classes/, file:/D:/MavenRepository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/D:/MavenRepository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/D:/MavenRepository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/D:/MavenRepository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/D:/MavenRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/D:/MavenRepository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/D:/MavenRepository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/D:/MavenRepository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/D:/MavenRepository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/MavenRepository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/D:/MavenRepository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/D:/MavenRepository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/D:/MavenRepository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/D:/MavenRepository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/MavenRepository/com/google/guava/guava/20.0/guava-20.0.jar, file:/D:/MavenRepository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/MavenRepository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/D:/MavenRepository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/MavenRepository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/MavenRepository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/D:/MavenRepository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/D:/MavenRepository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/MavenRepository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/MavenRepository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/D:/MavenRepository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/D:/MavenRepository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/D:/MavenRepository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/D:/MavenRepository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/D:/MavenRepository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/D:/MavenRepository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/D:/MavenRepository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/D:/MavenRepository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/D:/MavenRepository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/D:/MavenRepository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/D:/MavenRepository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/D:/MavenRepository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/D:/MavenRepository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/D:/MavenRepository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/D:/MavenRepository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/D:/MavenRepository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/D:/MavenRepository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/D:/MavenRepository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/D:/MavenRepository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/MavenRepository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/D:/MavenRepository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/D:/MavenRepository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/D:/MavenRepository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/D:/MavenRepository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/D:/MavenRepository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/D:/MavenRepository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/D:/MavenRepository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/D:/MavenRepository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/D:/MavenRepository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/D:/MavenRepository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/D:/MavenRepository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/D:/MavenRepository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/D:/MavenRepository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/D:/MavenRepository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/D:/MavenRepository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/D:/MavenRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/D:/MavenRepository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/D:/MavenRepository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/D:/MavenRepository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/D:/MavenRepository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/D:/MavenRepository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/MavenRepository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/D:/MavenRepository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/D:/MavenRepository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/D:/MavenRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/MavenRepository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/D:/MavenRepository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/D:/MavenRepository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/D:/MavenRepository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/D:/MavenRepository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/D:/MavenRepository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.0/mybatis-spring-boot-starter-2.2.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/D:/MavenRepository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.0/mybatis-spring-boot-autoconfigure-2.2.0.jar, file:/D:/MavenRepository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/MavenRepository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar, file:/D:/MavenRepository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/D:/MavenRepository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/D:/MavenRepository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/MavenRepository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/D:/MavenRepository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/MavenRepository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/D:/MavenRepository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/D:/MavenRepository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar, file:/D:/Idea/IntelliJ%20IDEA%202025.1.4.1/lib/idea_rt.jar]","logger_name":"org.springframework.boot.context.logging.ClasspathLoggingApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:28.554+08:00","@version":"1","message":"Starting GencoAdminApplication on LAPTOP-3I7P6FTL with PID 7308 (C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\api\\genco-admin\\target\\classes started by 吴兴龙 in C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\api)","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:28.557+08:00","@version":"1","message":"The following profiles are active: prod","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:28.557+08:00","@version":"1","message":"Loading source class com.genco.admin.GencoAdminApplication","logger_name":"org.springframework.boot.SpringApplication","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:28.606+08:00","@version":"1","message":"Activated activeProfiles prod","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:28.606+08:00","@version":"1","message":"Loaded config file 'file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-admin/target/classes/application.yml' (classpath:/application.yml)","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:28.606+08:00","@version":"1","message":"Profiles already activated, '[prod]' will not be applied","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:28.606+08:00","@version":"1","message":"Loaded config file 'file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-admin/target/classes/application-prod.yml' (classpath:/application-prod.yml) for profile prod","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:28.606+08:00","@version":"1","message":"Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7e3060d8","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:29.930+08:00","@version":"1","message":"Multiple Spring Data modules found, entering strict repository configuration mode!","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:29.932+08:00","@version":"1","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:29.976+08:00","@version":"1","message":"Finished Spring Data repository scanning in 30ms. Found 0 Redis repository interfaces.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:30.549+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:30.554+08:00","@version":"1","message":"Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:30.557+08:00","@version":"1","message":"Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@1eb85a47' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:30.558+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:30.564+08:00","@version":"1","message":"Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:30.788+08:00","@version":"1","message":"Code archive: D:\\MavenRepository\\org\\springframework\\boot\\spring-boot\\2.2.6.RELEASE\\spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:30.788+08:00","@version":"1","message":"Code archive: D:\\MavenRepository\\org\\springframework\\boot\\spring-boot\\2.2.6.RELEASE\\spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:30.790+08:00","@version":"1","message":"None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:30.821+08:00","@version":"1","message":"Tomcat initialized with port(s): 20000 (http)","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:30.829+08:00","@version":"1","message":"Initializing ProtocolHandler [\"http-nio-20000\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:30.830+08:00","@version":"1","message":"Starting service [Tomcat]","logger_name":"org.apache.catalina.core.StandardService","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:30.830+08:00","@version":"1","message":"Starting Servlet engine: [Apache Tomcat/9.0.33]","logger_name":"org.apache.catalina.core.StandardEngine","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:31.033+08:00","@version":"1","message":"At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.","logger_name":"org.apache.jasper.servlet.TldScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:31.039+08:00","@version":"1","message":"Initializing Spring embedded WebApplicationContext","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:31.040+08:00","@version":"1","message":"Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:31.040+08:00","@version":"1","message":"Root WebApplicationContext: initialization completed in 2434 ms","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:31.633+08:00","@version":"1","message":"Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:31.633+08:00","@version":"1","message":"Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:31.651+08:00","@version":"1","message":"Filter 'webMvcMetricsFilter' configured for use","logger_name":"org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:31.651+08:00","@version":"1","message":"Filter 'requestContextFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:31.652+08:00","@version":"1","message":"Filter 'corsFilter' configured for use","logger_name":"org.springframework.web.filter.CorsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:31.652+08:00","@version":"1","message":"Filter 'characterEncodingFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:31.652+08:00","@version":"1","message":"Filter 'springSecurityFilterChain' configured for use","logger_name":"org.springframework.boot.web.servlet.DelegatingFilterProxyRegistrationBean$1","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:31.652+08:00","@version":"1","message":"Filter 'formContentFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedFormContentFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:32.104+08:00","@version":"1","message":"Warn: Could not find @TableId in Class: com.genco.common.vo.UserFundsMonitor.","logger_name":"com.baomidou.mybatisplus.core.metadata.TableInfoHelper","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-08-06T08:55:32.329+08:00","@version":"1","message":"Warn: Could not find @TableId in Class: com.genco.common.model.order.StoreOrderStatus.","logger_name":"com.baomidou.mybatisplus.core.metadata.TableInfoHelper","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-08-06T08:55:32.595+08:00","@version":"1","message":"Warn: Could not find @TableId in Class: com.genco.common.model.system.SystemRoleMenu.","logger_name":"com.baomidou.mybatisplus.core.metadata.TableInfoHelper","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-08-06T08:55:35.084+08:00","@version":"1","message":"支付策略初始化完成，支持的支付类型: [offline, alipay, weixin, xendit, yue, haipay]","logger_name":"com.genco.service.service.impl.PaymentStrategyFactory","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:35.723+08:00","@version":"1","message":"298 mappings in 'requestMappingHandlerMapping'","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:35.764+08:00","@version":"1","message":"Exposing 2 endpoint(s) beneath base path '/actuator'","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:35.840+08:00","@version":"1","message":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","logger_name":"springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:35.861+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:35.862+08:00","@version":"1","message":"Initializing ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:35.977+08:00","@version":"1","message":"Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@779ef5cb, org.springframework.security.web.context.SecurityContextPersistenceFilter@3a44993c, org.springframework.security.web.header.HeaderWriterFilter@3ea1729e, org.springframework.web.filter.CorsFilter@545604a9, org.springframework.web.filter.CorsFilter@545604a9, org.springframework.web.filter.CorsFilter@545604a9, org.springframework.security.web.authentication.logout.LogoutFilter@790d629a, com.genco.admin.filter.JwtAuthenticationTokenFilter@794d28a3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@27605b87, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4700963e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5d9d8ecf, org.springframework.security.web.session.SessionManagementFilter@301f9aa0, org.springframework.security.web.access.ExceptionTranslationFilter@53b8a0f7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5b74bb75]","logger_name":"org.springframework.security.web.DefaultSecurityFilterChain","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.116+08:00","@version":"1","message":"Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'","logger_name":"org.springframework.boot.admin.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:36.125+08:00","@version":"1","message":"ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:36.173+08:00","@version":"1","message":"Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:36.186+08:00","@version":"1","message":"ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:36.554+08:00","@version":"1","message":"Context refreshed","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.568+08:00","@version":"1","message":"Found 2 custom documentation plugin(s)","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.599+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.770+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.776+08:00","@version":"1","message":"Generating unique operation named: getByIdsUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.781+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.787+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.794+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.797+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.822+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.825+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.837+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.839+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.850+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.885+08:00","@version":"1","message":"Generating unique operation named: updatePhoneUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.890+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.906+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.908+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.911+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.913+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.913+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.914+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.916+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.919+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.930+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.934+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.938+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.939+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.947+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.955+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.957+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.960+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.961+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.961+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.971+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.976+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.976+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.982+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.987+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:36.995+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.007+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.019+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.026+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.041+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.047+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.052+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.053+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.056+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.061+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.063+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.064+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.066+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.069+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.070+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.071+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.072+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.076+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.078+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.086+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.087+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.089+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.089+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.090+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.090+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.093+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.093+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.094+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.095+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.096+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.096+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.100+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.101+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.102+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.103+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.105+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.106+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.107+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.108+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.109+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.112+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.113+08:00","@version":"1","message":"Generating unique operation named: getListTreeUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.114+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.116+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.117+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.119+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.125+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.126+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.127+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.127+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.128+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.131+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.132+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.134+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.134+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.135+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.138+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.139+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.140+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.140+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.142+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.145+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.146+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.146+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.149+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.151+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.153+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.154+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.157+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.158+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.159+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.159+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.160+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.161+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.164+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_29","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.165+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.166+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.167+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.167+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.169+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.172+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_30","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.173+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.174+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.175+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.176+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.176+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.177+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_31","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.179+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.180+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.214+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_32","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.216+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.221+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.230+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_33","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.233+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.234+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.235+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.237+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_34","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.238+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.238+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.238+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.242+08:00","@version":"1","message":"Generating unique operation named: getListUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.245+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_35","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.246+08:00","@version":"1","message":"Generating unique operation named: balanceUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.252+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_36","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.253+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.255+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_37","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.255+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.256+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.257+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.271+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.283+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.287+08:00","@version":"1","message":"---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Wed Aug 06 08:55:37 CST 2025","logger_name":"com.genco.admin.task.tiktok.TiktokTokenRefreshTask","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.288+08:00","@version":"1","message":"Starting ProtocolHandler [\"http-nio-20000\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.319+08:00","@version":"1","message":"Tomcat started on port(s): 20000 (http) with context path ''","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.321+08:00","@version":"1","message":"Started GencoAdminApplication in 9.266 seconds (JVM running for 9.947)","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.374+08:00","@version":"1","message":"{dataSource-1} inited","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.732+08:00","@version":"1","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"RMI TCP Connection(1)-************","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.732+08:00","@version":"1","message":"Initializing Servlet 'dispatcherServlet'","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"RMI TCP Connection(1)-************","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:37.732+08:00","@version":"1","message":"Detected StandardServletMultipartResolver","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"RMI TCP Connection(1)-************","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:37.740+08:00","@version":"1","message":"enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"RMI TCP Connection(1)-************","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:37.741+08:00","@version":"1","message":"Completed initialization in 9 ms","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"RMI TCP Connection(1)-************","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:38.459+08:00","@version":"1","message":"Tiktok access_token 未到刷新时间，当前时间: 1754441737，过期时间: 1781838424","logger_name":"com.genco.admin.task.tiktok.TiktokTokenRefreshTask","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:38.462+08:00","@version":"1","message":"Executing SQL query [/* ping */ SELECT 1]","logger_name":"org.springframework.jdbc.core.JdbcTemplate","thread_name":"RMI TCP Connection(3)-************","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-06T08:55:38.943+08:00","@version":"1","message":"开始同步TikTok订单 - pageToken: null, startTime: Sat Aug 02 23:07:02 CST 2025, endTime: Tue Aug 05 23:07:02 CST 2025","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:40.109+08:00","@version":"1","message":"开始调用TikTok API获取订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:41.291+08:00","@version":"1","message":"API调用成功，获取到下一页token: , 开始处理订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:41.375+08:00","@version":"1","message":"开始处理 1 个订单","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:41.486+08:00","@version":"1","message":"订单批量处理完成 - 总计: 1, 新增: 0, 更新: 0, 跳过: 1, 错误: 0","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:41.647+08:00","@version":"1","message":"TikTok订单同步事务执行完成，下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-06T08:55:41.647+08:00","@version":"1","message":"TikTok订单同步方法执行完成，返回下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
